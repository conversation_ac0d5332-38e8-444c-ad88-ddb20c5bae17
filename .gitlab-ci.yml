variables:
  SMARTANALYTICS_PROCESSORS_PROJECT_ID: 444
  PACKAGE_NAME: "smartanalytics-processors"
  SMARTANALYTICS_PROCESSORS_VERSION: "latest"
    # READ_PACKAGES_TOKEN: ""

  DOCKER_TERRAGRUNT_TAG: aws-tf-1.12.2-tg-0.83.0
  PLAN: plan.tfplan
  JSON_PLAN_FILE: tfplan.json
  VAULT_VERSION: 1.13.2
  TRIVY_VERSION: 0.47.0
  CHECKSUMS: |-
    f7930279de8381de7c532164b4a4408895d9606c0d24e2e9d2f9acb5dfe99b3c  /tmp/vault.zip
  TF_PLUGIN_CACHE_DIR: ${CI_PROJECT_DIR}/.terraform.d/plugin-cache
  TFLINT_PLUGIN_DIR: ${CI_PROJECT_DIR}/.tflint.d/plugins

workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS
      when: never
    - if: $CI_COMMIT_BRANCH
    - if: $CI_COMMIT_TAG =~ /^rel-/

default:
  image: 
    name: devopsinfra/docker-terragrunt:$DOCKER_TERRAGRUNT_TAG
    pull_policy:
      - if-not-present
      - always
  cache:
    paths:
      - ${TFLINT_PLUGIN_DIR}
      - ${TF_PLUGIN_CACHE_DIR}
  before_script:
    - set -euo pipefail
    - mkdir -p "${TF_PLUGIN_CACHE_DIR}" "${TFLINT_PLUGIN_DIR}" || true
    # MR plan summary helper
    - shopt -s expand_aliases
    - alias convert_report="jq -r '([.resource_changes[]?.change.actions?]|flatten)|{\"create\":(map(select(.==\"create\"))|length),\"update\":(map(select(.==\"update\"))|length),\"delete\":(map(select(.==\"delete\"))|length)}'"

    # Vault
    - curl -sSL -o /tmp/vault.zip "https://releases.hashicorp.com/vault/${VAULT_VERSION}/vault_${VAULT_VERSION}_linux_amd64.zip"
    - echo "${CHECKSUMS}" | (cd /tmp; sha256sum -c -w -)
    - unzip -q /tmp/vault.zip -d /tmp
    - install -b -c -v /tmp/vault /usr/bin/
    - vault version

    # Tooling info
    - terragrunt -version
    - terraform -version
    - tflint --version || true

    # Git CI token for cross-project clones if needed
    - git config --global url."https://gitlab-ci-token:${CI_JOB_TOKEN}@${CI_SERVER_HOST}".insteadOf "ssh://git@${CI_SERVER_HOST}"

    # Download Lambda packages (Generic Package Registry)
    - mkdir -p /tmp/lambda_packages/artifacts
    - |
      set -euo pipefail

      PACKAGE_VERSION="${SMARTANALYTICS_PROCESSORS_VERSION:-latest}"
      echo "Downloading Smart Analytics Processors packages: version=${PACKAGE_VERSION}"

      # Choose registry auth header
      if [[ -n "${READ_PACKAGES_TOKEN:-}" ]]; then
        AUTH_HEADER="PRIVATE-TOKEN: ${READ_PACKAGES_TOKEN}"
      else
        AUTH_HEADER="JOB-TOKEN: ${CI_JOB_TOKEN}"
      fi

      BASE_URL="${CI_API_V4_URL}/projects/${SMARTANALYTICS_PROCESSORS_PROJECT_ID}/packages/generic/${PACKAGE_NAME}/${PACKAGE_VERSION}"

      echo "Fetching package manifest..."
      curl --fail -sSL -H "${AUTH_HEADER}" -o "/tmp/lambda_packages/package-manifest.json" "${BASE_URL}/package-manifest.json" || true

      # Default function list (will be overridden by manifest if present)
      LAMBDA_FUNCTIONS=("agent-event-processor")

      if [[ -f "/tmp/lambda_packages/package-manifest.json" ]]; then
        echo "Manifest found; deriving function list..."
        if jq -e '.functions|length>0' /tmp/lambda_packages/package-manifest.json >/dev/null 2>&1; then
          mapfile -t LAMBDA_FUNCTIONS < <(jq -r '.functions[].name' /tmp/lambda_packages/package-manifest.json)
        fi
        echo "Manifest:"
        jq . /tmp/lambda_packages/package-manifest.json || true
      fi

      echo "Functions to download: ${LAMBDA_FUNCTIONS[*]}"

      for func in "${LAMBDA_FUNCTIONS[@]}"; do
        echo "Downloading ${func}.zip ..."
        curl --fail -sSL -H "${AUTH_HEADER}" \
          -o "/tmp/lambda_packages/artifacts/${func}.zip" \
          "${BASE_URL}/${func}.zip"

        ZIP="/tmp/lambda_packages/artifacts/${func}.zip"
        test -f "${ZIP}" || { echo "ERROR: ${func}.zip missing"; exit 1; }

        size_bytes="$(stat -c%s "${ZIP}" 2>/dev/null || stat -f%z "${ZIP}" 2>/dev/null || echo "0")"
        echo "Downloaded ${func}.zip (${size_bytes} bytes)"
        unzip -l "${ZIP}" | head -15 || true
      done

  tags:
    - codebuild-gitlab-ci-${CI_PROJECT_NAMESPACE_SLUG}-$CI_PROJECT_ID-$CI_PIPELINE_IID-$CI_JOB_NAME
    - image:custom-linux-devopsinfra/docker-terragrunt:${DOCKER_TERRAGRUNT_TAG}

stages:
  - validate
  - plan
  - deploy

# ---- Validate whole stack ----
validate:
  stage: validate
  script:
    - echo "Validating Terragrunt configuration..."
    - cd live/
    - TERRAGRUNT_DISABLE_INIT=true terragrunt run-all validate --terragrunt-non-interactive
    - echo "Validation completed."

# ---- PLAN template ----
.plan-template:
  stage: plan
  resource_group: "tfstate-${ENVIRONMENT}"
  rules:
    - when: never
  id_tokens:
    VAULT_TOKEN:
      aud: https://vault.int.state911.net
  script:
    - echo "Starting PLAN for ${PARTITION}/${ENVIRONMENT}..."
    - export VAULT_ADDR="https://vault.int.state911.net"
    - export VAULT_TOKEN="$(vault write -field=token auth/jwt/login role=ci-${CI_PROJECT_NAMESPACE}-${CI_PROJECT_NAME} jwt=${VAULT_TOKEN})"

    # AWS creds
    - export AWS_ACCESS_KEY_ID="$(vault kv get -format=json secret/gitlab-ci/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}/${PARTITION}-ci-config | jq -r .data.data.access_key)"
    - export AWS_SECRET_ACCESS_KEY="$(vault kv get -format=json secret/gitlab-ci/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}/${PARTITION}-ci-config | jq -r .data.data.secret_access_key)"
    - export ROLE_ARN="$(vault kv get -format=json secret/gitlab-ci/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}/${PARTITION}-ci-config | jq -r ".data.data.ci_role_arn_list[\"$ACCOUNT\"]")"

    # AWS config
    - mkdir -p "${HOME}/.aws" && chmod 700 "${HOME}/.aws"
    - printf "[default]\nregion = %s\n" "${REGION}" > "${HOME}/.aws/config"
    - export AWS_REGION="${REGION}"

    - mkdir -p "live/${PARTITION}/${ENVIRONMENT}/artifacts"
    - cp -f /tmp/lambda_packages/artifacts/*.zip "live/${PARTITION}/${ENVIRONMENT}/artifacts/" 2>/dev/null || true
    - echo "Artifacts present:"
    - ls -la "live/${PARTITION}/${ENVIRONMENT}/artifacts" || true

    # Run plan in env directory
    - cd "live/${PARTITION}/${ENVIRONMENT}"

    - echo "Render Terragrunt inputs..."
    - terragrunt render-json --terragrunt-non-interactive --terragrunt-iam-role "${ROLE_ARN}" --terragrunt-iam-assume-role-session-name "gitlab-ci-${CI_PROJECT_NAMESPACE}-${CI_PROJECT_NAME}"
    - jq . terragrunt_rendered.json || true

    - echo "Validate..."
    - terragrunt validate --terragrunt-non-interactive --terragrunt-iam-role "${ROLE_ARN}" --terragrunt-iam-assume-role-session-name "gitlab-ci-${CI_PROJECT_NAMESPACE}-${CI_PROJECT_NAME}"

    - echo "Plan..."
    - terragrunt plan --terragrunt-non-interactive --terragrunt-iam-role "${ROLE_ARN}" --terragrunt-iam-assume-role-session-name "gitlab-ci-${CI_PROJECT_NAMESPACE}-${CI_PROJECT_NAME}" -out "$(pwd)/${ENVIRONMENT}-${PLAN}"

    - echo "Plan JSON summary..."
    - terragrunt show --terragrunt-non-interactive --terragrunt-iam-role "${ROLE_ARN}" --terragrunt-iam-assume-role-session-name "gitlab-ci-${CI_PROJECT_NAMESPACE}-${CI_PROJECT_NAME}" --json "$(pwd)/${ENVIRONMENT}-${PLAN}" | convert_report > "$(pwd)/${ENVIRONMENT}-${JSON_PLAN_FILE}"

  after_script:
    - rm -rf "${HOME}/.aws/"

  artifacts:
    name: "terraform-plan-${ENVIRONMENT}"
    expire_in: 1 week
    paths:
      - "live/${PARTITION}/${ENVIRONMENT}/${ENVIRONMENT}-${PLAN}"
      - "live/${PARTITION}/${ENVIRONMENT}/.terragrunt-cache/"
    reports:
      terraform: "live/${PARTITION}/${ENVIRONMENT}/${ENVIRONMENT}-${JSON_PLAN_FILE}"

# ---- PLAN jobs ----
plan-dev-us:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-us
    ACCOUNT: dev-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "data-analytics"
      when: never
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

#plan-qa-us:
#  extends: .plan-template
#  variables:
#    REGION: us-east-1
#    ENVIRONMENT: qa-us
#    ACCOUNT: dev-nsoc
#    PARTITION: aws
#  rules:
#    - if: $CI_PROJECT_NAMESPACE != "data-analytics"
#      when: never
#    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
#    - if: $CI_COMMIT_BRANCH == "main"

#plan-prod-us:
#  extends: .plan-template
#  variables:
#    REGION: us-east-1
#    ENVIRONMENT: prod-us
#    ACCOUNT: prod-nsoc
#    PARTITION: aws
#  rules:
#    - if: $CI_PROJECT_NAMESPACE != "data-analytics"
#      when: never
#    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
#    - if: $CI_COMMIT_BRANCH == "main"

#plan-prod-ca:
#  extends: .plan-template
#  variables:
#    REGION: ca-central-1
#    ENVIRONMENT: prod-ca
#    ACCOUNT: prod-nsoc
#    PARTITION: aws
#  rules:
#    - if: $CI_PROJECT_NAMESPACE != "data-analytics"
#      when: never
#    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
#    - if: $CI_COMMIT_BRANCH == "main"

# ---- DEPLOY template ----
.deploy-template:
  stage: deploy
  resource_group: "tfstate-${ENVIRONMENT}"
  rules:
    - when: never
  id_tokens:
    VAULT_TOKEN:
      aud: https://vault.int.state911.net
  script:
    - echo "Starting DEPLOY for ${PARTITION}/${ENVIRONMENT}..."
    - export VAULT_ADDR="https://vault.int.state911.net"
    - export VAULT_TOKEN="$(vault write -field=token auth/jwt/login role=ci-${CI_PROJECT_NAMESPACE}-${CI_PROJECT_NAME} jwt=${VAULT_TOKEN})"

    # AWS creds
    - export AWS_ACCESS_KEY_ID="$(vault kv get -format=json secret/gitlab-ci/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}/${PARTITION}-ci-config | jq -r .data.data.access_key)"
    - export AWS_SECRET_ACCESS_KEY="$(vault kv get -format=json secret/gitlab-ci/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}/${PARTITION}-ci-config | jq -r .data.data.secret_access_key)"
    - export ROLE_ARN="$(vault kv get -format=json secret/gitlab-ci/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}/${PARTITION}-ci-config | jq -r ".data.data.ci_role_arn_list[\"$ACCOUNT\"]")"

    # AWS config
    - mkdir -p "${HOME}/.aws" && chmod 700 "${HOME}/.aws"
    - printf "[default]\nregion = %s\n" "${REGION}" > "${HOME}/.aws/config"
    - export AWS_REGION="${REGION}"

    # Ensure artifacts present (fresh runner)
    - mkdir -p "live/${PARTITION}/${ENVIRONMENT}/artifacts"
    - cp -f /tmp/lambda_packages/artifacts/*.zip "live/${PARTITION}/${ENVIRONMENT}/artifacts/" 2>/dev/null || true
    - echo "Artifacts present:"
    - ls -la "live/${PARTITION}/${ENVIRONMENT}/artifacts" || true

    - cd "live/${PARTITION}/${ENVIRONMENT}"

    - echo "Render Terragrunt inputs..."
    - terragrunt render-json --terragrunt-non-interactive --terragrunt-iam-role "${ROLE_ARN}" --terragrunt-iam-assume-role-session-name "gitlab-ci-${CI_PROJECT_NAMESPACE}-${CI_PROJECT_NAME}"
    - jq . terragrunt_rendered.json || true

    - echo "Apply previously saved plan..."
    - terragrunt apply --terragrunt-non-interactive --terragrunt-iam-role "${ROLE_ARN}" --terragrunt-iam-assume-role-session-name "gitlab-ci-${CI_PROJECT_NAMESPACE}-${CI_PROJECT_NAME}" "$(pwd)/${ENVIRONMENT}-${PLAN}"
    - echo "Deployment completed."
  after_script:
    - rm -rf "${HOME}/.aws/"

# ---- DEPLOY jobs ----
deploy-dev-us:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-us
    ACCOUNT: dev-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}
    deployment_tier: development
  rules:
    - if: $CI_PROJECT_NAMESPACE != "data-analytics"
      when: never
    # TODO: Uncomment when ready to deploy
    #- if: $CI_COMMIT_BRANCH == "main"
    #  when: manual
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs: [plan-dev-us]

#deploy-qa-us:
#  extends: .deploy-template
#  variables:
#    REGION: us-east-1
#    ENVIRONMENT: qa-us
#    ACCOUNT: dev-nsoc
#    PARTITION: aws
#  environment:
#    name: ${ENVIRONMENT}
#    deployment_tier: testing
#  rules:
#    - if: $CI_PROJECT_NAMESPACE != "data-analytics"
#      when: never
#    - if: $CI_COMMIT_BRANCH == "main"
#      when: manual
#  needs: [plan-qa-us]

#deploy-prod-us:
#  extends: .deploy-template
#  variables:
#    REGION: us-east-1
#    ENVIRONMENT: prod-us
#    ACCOUNT: prod-nsoc
#    PARTITION: aws
#  environment:
#    name: ${ENVIRONMENT}
#    deployment_tier: production
#  rules:
#    - if: $CI_PROJECT_NAMESPACE != "data-analytics"
#      when: never
#    - if: $CI_COMMIT_BRANCH == "main"
#      when: manual
#  needs: [plan-prod-us]

#deploy-prod-ca:
#  extends: .deploy-template
#  variables:
#    REGION: ca-central-1
#    ENVIRONMENT: prod-ca
#    ACCOUNT: prod-nsoc
#    PARTITION: aws
#  environment:
#    name: ${ENVIRONMENT}
#    deployment_tier: production
#  rules:
#    - if: $CI_PROJECT_NAMESPACE != "data-analytics"
#      when: never
#    - if: $CI_COMMIT_BRANCH == "main"
#      when: manual
#  needs: [plan-prod-ca]