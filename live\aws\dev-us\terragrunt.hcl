# Include the root terragrunt configuration
include "root" {
  path = find_in_parent_folders()
}

# Reference the Terraform module from the main repository
terraform {
  # TODO: Change ref to develop before merging
source = "git::ssh://****************************/data-analytics/smartanalytics-processors.git//?ref=feat/CSA-242/acd-processor-code"
}

# Environment-specific inputs
inputs = {
  # AWS Configuration
  region      = "us-east-1"
  environment = "dev"
  country     = "us"

  # Global tags
  global_tags = {
    Environment = "dev"
    Project     = "smartanalytics"
    ManagedBy   = "terragrunt"
    Country     = "us"
  }

  # SNS topic for critical alerts
  slack_errors_alarm_topic_name = "dev-us-smartanalytics-alarms"

  # Project directory - this will be the downloaded source
  project_dir = get_terragrunt_dir()

  # Customer configuration
  service_customer_config = {
    pvgt = {
      s3_lambda_code_bucket_name = "dev-us-smartanalytics-pvgt-lambdas"
      kms_key_name = "alias/dev-us-smartanalytics-pvgt-key"
      
      acd = {
        enable = true
        queue_name = "dev-us-smartanalytics-pvgt-acd-queue"
        dlq_name   = "dev-us-smartanalytics-pvgt-acd-dlq"
        
        # VPC configuration
        subnet_ids         = ["subnet-0b464cddd18b1a41d", "subnet-0d567e2f525d21d11"]
        vpc_id             = "vpc-0dadc21fb881c5789"
        
        # Redshift configuration
        redshift_secret_name         = "/dev/us/smartanalytics/common_redshift_credentials"
        redshift_cluster_identifier  = "dev-us-smartanalytics-common-redshift"
        
        environment_variables = {
          # Core application settings
          ENVIRONMENT = "dev"  # or "qa", "prod"
          
          # Logging configuration
          LOG_LEVEL = "INFO"  # or "DEBUG", "WARNING", "ERROR"
          
          # AWS configuration
          AWS_REGION = "us-east-1"
          
          # Database/Redshift configuration
          DATABASE_CLUSTER_IDENTIFIER     = "dev-us-smartanalytics-common-redshift"
          DATABASE_REDSHIFT_DATABASE      = "dev"
          DATABASE_REDSHIFT_USER          = "solacom"
          DATABASE_QUERY_GROUP           = "agent-event-processor"
          DATABASE_QUERY_TIMEOUT         = "300"
          
          # Client configuration
          CLIENT_NAME     = "pvgt"
          CLIENT_TIMEZONE = "America/New_York"
          
          # Lambda Powertools
          POWERTOOLS_SERVICE_NAME = "agent-event-processor"
          POWERTOOLS_LOG_LEVEL   = "INFO"
        }
      }
      
      tags = {
        Customer      = "pvgt"
        billing_code  = "cust_pvgt_dev"
        # TODO: Add approval date and cost center
        ApprovalDate = ""
        ChargeCode = ""
        Tier       = "development"
      }
    }
  }
}