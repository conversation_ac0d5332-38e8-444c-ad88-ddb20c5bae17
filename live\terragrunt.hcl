locals {
  # Mock account ID to allow validating w/o AWS credentials.
  aws_account_id = try(
    get_aws_account_id(),
    "************",
  )

  # terragrunt run-all validate fails in live/ because it can't find env.hcl, so
  # put a fallback with an invalid value to get past that behavior.
  env_vars = try(
    read_terragrunt_config(find_in_parent_folders("env.hcl")),
    {
      locals = {
        tfstate_region = "fake"
      }
    }
  )

  tags = {
    terragrunt_repo = "gitlab.nsoc.state911.net/data-analytics/smartanalytics-processors-deploy"
    terragrunt_path = get_path_from_repo_root()
  }
}

remote_state {
  backend      = "s3"
  disable_init = tobool(get_env("TERRAGRUNT_DISABLE_INIT", "false"))
  generate = {
    path      = "backend.tf"
    if_exists = "overwrite_terragrunt"
  }
  config = {
    bucket         = "terraform-state-${local.aws_account_id}"
    dynamodb_table = "terraform-locks"
    encrypt        = true
    key            = "smartanalytics-processors-deploy/${path_relative_to_include()}/terraform.tfstate"
    region         = local.env_vars.locals.tfstate_region
  }
}

terraform {
  before_hook "tflint" {
    commands = [
      "apply",
      "plan",
      "validate",
    ]
    execute = [
      "tflint"
    ]
  }
}
